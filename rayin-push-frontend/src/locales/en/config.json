{"title": "Interface Config", "createConfig": "Create Config", "createConfigDesc": "Create Interface Config", "editConfig": "Edit Config", "editConfigDesc": "Edit Interface Config", "deleteConfig": "Delete Config", "configName": "Config Name", "configToken": "Config <PERSON>", "configDescription": "Config Description", "requestMethod": "Request Method", "parsingRules": "Parsing Rules", "basicInfo": "Basic Info", "channelSettings": "Channel Settings", "configStatus": "Config Status", "enabled": "Enabled", "disabled": "Disabled", "enableConfig": "Enable Config", "disableConfig": "Disable Config", "searchPlaceholder": "Search config name or description...", "allStatus": "All Status", "noConfigData": "No configuration data", "required": "Required", "configNamePlaceholder": "Enter configuration name", "configDescPlaceholder": "Enter configuration description", "selectMethodPlaceholder": "Select request method", "selectChannelPlaceholder": "Select notification channels", "configNameRequired": "Configuration name is required", "requestMethodRequired": "Request method is required", "channelRequired": "At least one notification channel must be selected", "selectMethodFirst": "Please select request method first", "selectMethodHint": "You need to select a request method before configuring parsing rules", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "newParam": "New Parameter", "variable": "Variable", "messageExtractionRules": "Message Extraction Rules", "contentType": "Content Type", "variableMapping": "Variable Mapping", "addMapping": "Add Mapping", "noParameterMapping": "No parameter mapping, click \"Add Mapping\" to start configuration", "jsonPathPlaceholder": "JSON path (e.g.: user.name, info.address)", "interfaceParamPlaceholder": "Interface parameter name", "templateVariablePlaceholder": "Template variable name", "regexRules": "Regular Expression Rules", "addRule": "Add Rule", "noRegexRules": "No regex rules, click \"Add Rule\" to start configuration", "ruleNumber": "Rule {{number}}", "variableName": "Variable Name", "regexExpression": "Regular Expression", "templateVariableNamePlaceholder": "Variable name used in template", "regexPatternPlaceholder": "e.g.: My name is (.+)", "requestParamPlaceholder": "Request Parameter", "variableNamePlaceholder": "Variable Name", "regexVariableNamePlaceholder": "Variable Name", "regexExpressionPlaceholder": "Regular Expression", "formFieldMapping": "Configure form field mapping, map interface field names to template variable names", "jsonFieldMapping": "Configure JSON field path mapping, support extracting multi-level fields", "regexExtraction": "Use regular expressions to extract data from text, each regex extracts one variable"}